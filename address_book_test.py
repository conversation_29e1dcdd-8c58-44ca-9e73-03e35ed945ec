import json
import socket
import struct

def ip_to_decimal(ip_str):
    """
    将IP地址字符串转换为10进制数
    """
    try:
        return struct.unpack("!I", socket.inet_aton(ip_str))[0]
    except socket.error:
        print(f"警告: 无效的IP地址 {ip_str}")
        return None

def test_ip_conversion():
    """
    测试IP地址转换功能
    """
    print("=== IP地址转换测试 ===")
    
    test_ips = [
        "*************",
        "*********", 
        "***********",
        "************",
        "**************",  # 示例IP: 1909854954
        "**************",  # 示例IP: 1909854955
        "**************"   # 示例IP: 1909854960
    ]
    
    for ip in test_ips:
        decimal = ip_to_decimal(ip)
        print(f"{ip} -> {decimal}")
    
    return test_ips

def construct_mock_login_data():
    """
    构造模拟登录数据
    """
    return {
        "data": {
            "PHPSESSID": "i9cnjk2b8tgk9pnb0jivmltfls",
            "uuid": "e6d8ea61-1c6c-7849-ad18-093e5361f433",
            "role": "api地址簿接口",
            "username": "addbook",
            "vsysId": "0",
            "token": "CzZe6NAwPupfeA9xfmHIn57NJjSJ1IEK4ShL6K4",
            "user_id": "12345",
            "session_timeout": "3600"
        }
    }

def construct_cookie_from_login_data(login_data):
    """
    从登录数据构造Cookie字符串
    """
    if not login_data or 'data' not in login_data:
        print("警告: 登录数据中没有找到data字段")
        return ""
    
    data = login_data['data']
    cookie_parts = []
    
    # 遍历data中的所有属性构造cookie
    for key, value in data.items():
        if isinstance(value, (str, int, float)):
            # URL编码特殊字符
            if isinstance(value, str) and any(c in value for c in [' ', '/', '=', '&']):
                import urllib.parse
                value = urllib.parse.quote(str(value))
            cookie_parts.append(f"{key}={value}")
    
    # 添加一些必要的cookie字段
    essential_cookies = [
        "vsysName=root",
        "fromrootvsys=true", 
        "zoneinfo=Asia%2FChongqing"
    ]
    
    cookie_parts.extend(essential_cookies)
    cookie_string = "; ".join(cookie_parts)
    
    print(f"构造的Cookie: {cookie_string}")
    print(f"Cookie长度: {len(cookie_string)} 字符")
    return cookie_string

def construct_address_book_payload(attack_ips):
    """
    构造地址簿负载
    """
    print("=== 构造地址簿负载 ===")
    
    # 构造IP列表
    ip_list = []
    for ip_str in attack_ips:
        ip_decimal = ip_to_decimal(ip_str)
        if ip_decimal is not None:
            ip_entry = {
                "ip_addr": ip_decimal,
                "netmask": 32,
                "flag": 0
            }
            ip_list.append(ip_entry)
            print(f"添加IP: {ip_str} -> {ip_decimal}")
    
    # 构造完整负载
    payload = [{
        "is_ipv6": "0",
        "type": "0", 
        "name": "地址簿08",
        "description": "api封禁",
        "entry": [],
        "ip": ip_list,
        "range": [],
        "host": [],
        "wildcard": [],
        "country": []
    }]
    
    return payload

def generate_curl_command(login_data, payload):
    """
    生成curl命令用于手动测试
    """
    print("=== 生成curl命令 ===")
    
    cookie_string = construct_cookie_from_login_data(login_data)
    token = login_data['data'].get('token', '')
    
    # 保存负载到文件
    with open('address_book_payload.json', 'w', encoding='utf-8') as f:
        json.dump(payload, f, ensure_ascii=False, indent=2)
    
    curl_cmd = f'''curl -X PUT \\
  "https://********:8443/rest/addrbook_address?isTransaction=1&idfield=is_ipv6%2Ctype%2Cname" \\
  -H "Accept: */*" \\
  -H "Accept-Encoding: gzip, deflate, br, zstd" \\
  -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" \\
  -H "Connection: keep-alive" \\
  -H "Content-Type: application/json;charset=UTF-8" \\
  -H "Cookie: {cookie_string}" \\
  -H "Host: ********:8443" \\
  -H "Referer: https://********:8443/" \\
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" \\
  -H "X-API-Language: zh_CN" \\
  -H "X-API-Version: 1" \\
  -H "X-Auth-Token: {token}" \\
  -H "X-Requested-With: XMLHttpRequest" \\
  -k --insecure \\
  --data-binary @address_book_payload.json'''
    
    # 保存curl命令到文件
    with open('address_book_curl.sh', 'w', encoding='utf-8') as f:
        f.write(curl_cmd)
    
    # 生成Windows批处理版本
    with open('address_book_curl.bat', 'w', encoding='utf-8') as f:
        f.write('@echo off\n')
        f.write('echo 正在发送地址簿PUT请求...\n')
        f.write(curl_cmd.replace('\\', '^'))
        f.write('\npause\n')
    
    print("✓ curl命令已保存到:")
    print("  - address_book_curl.sh (Linux/Mac)")
    print("  - address_book_curl.bat (Windows)")
    print("  - address_book_payload.json (负载数据)")

def main():
    """
    主函数 - 测试版本
    """
    print("地址簿操作工具 - 测试版本")
    print("=" * 50)
    
    # 1. 测试IP转换
    test_ips = test_ip_conversion()
    
    # 2. 构造模拟登录数据
    print("\n=== 构造模拟登录数据 ===")
    login_data = construct_mock_login_data()
    print("模拟登录数据:")
    print(json.dumps(login_data, ensure_ascii=False, indent=2))
    
    # 3. 构造Cookie
    print("\n=== 构造Cookie ===")
    cookie_string = construct_cookie_from_login_data(login_data)
    
    # 4. 构造地址簿负载
    payload = construct_address_book_payload(test_ips)
    print(f"\n负载包含 {len(payload[0]['ip'])} 个IP地址")
    
    # 5. 显示负载预览
    print("\n=== 负载预览 ===")
    print(json.dumps(payload, ensure_ascii=False, indent=2)[:500] + "...")
    
    # 6. 生成curl命令
    generate_curl_command(login_data, payload)
    
    # 7. 保存完整测试结果
    test_result = {
        "login_data": login_data,
        "cookie_string": cookie_string,
        "payload": payload,
        "ip_count": len(payload[0]['ip']),
        "test_ips": test_ips
    }
    
    with open('address_book_test_result.json', 'w', encoding='utf-8') as f:
        json.dump(test_result, f, ensure_ascii=False, indent=2)
    
    print("\n🎉 测试完成！")
    print("=" * 50)
    print("生成的文件:")
    print("- address_book_test_result.json: 完整测试结果")
    print("- address_book_payload.json: PUT请求负载")
    print("- address_book_curl.sh: Linux/Mac curl命令")
    print("- address_book_curl.bat: Windows curl命令")
    print("\n在正确的网络环境中，可以:")
    print("1. 运行 address_book_operation.py 进行完整操作")
    print("2. 手动执行生成的curl命令进行测试")

if __name__ == "__main__":
    main()
