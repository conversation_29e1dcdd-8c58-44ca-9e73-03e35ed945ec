import json

def test_data_extraction():
    """
    使用模拟数据测试attack_ip提取功能
    """
    # 模拟API响应数据
    mock_response = {
        "total": 100,
        "success": True,
        "data": [
            {
                "attack_ip": "*************",
                "attack_time": "2024-01-01 10:00:00",
                "attack_type": "SQL注入",
                "severity": "高"
            },
            {
                "attack_ip": "*********",
                "attack_time": "2024-01-01 10:05:00",
                "attack_type": "XSS攻击",
                "severity": "中"
            },
            {
                "attack_ip": "***********",
                "attack_time": "2024-01-01 10:10:00",
                "attack_type": "暴力破解",
                "severity": "高"
            },
            {
                "attack_ip": "*************",  # 重复IP
                "attack_time": "2024-01-01 10:15:00",
                "attack_type": "端口扫描",
                "severity": "低"
            },
            {
                "attack_ip": "************",
                "attack_time": "2024-01-01 10:20:00",
                "attack_type": "DDoS攻击",
                "severity": "高"
            }
        ]
    }
    
    print("=== 模拟数据测试 ===")
    print(f"模拟响应总数: {mock_response.get('total', 0)}")
    print(f"请求成功状态: {mock_response.get('success', False)}")
    
    # 提取data字段
    data = mock_response.get('data', [])
    
    # 提取attack_ip属性
    attack_ips = []
    if isinstance(data, list):
        for item in data:
            if isinstance(item, dict) and 'attack_ip' in item:
                attack_ips.append(item['attack_ip'])
    
    print(f"成功提取到 {len(attack_ips)} 个attack_ip")
    
    if attack_ips:
        print("\n提取到的attack_ip列表:")
        for i, ip in enumerate(attack_ips, 1):
            print(f"{i}. {ip}")
        
        # 保存到文件
        with open('test_attack_ips.txt', 'w', encoding='utf-8') as f:
            for ip in attack_ips:
                f.write(f"{ip}\n")
        print(f"\nattack_ip列表已保存到 test_attack_ips.txt 文件")
        
        # 统计唯一IP
        unique_ips = list(set(attack_ips))
        print(f"\n统计信息:")
        print(f"总计attack_ip数量: {len(attack_ips)}")
        print(f"唯一attack_ip数量: {len(unique_ips)}")
        
        print(f"\n唯一IP列表:")
        for i, ip in enumerate(unique_ips, 1):
            print(f"{i}. {ip}")
    
    return attack_ips

def show_request_details():
    """
    显示将要发送的请求详情
    """
    print("\n=== 请求详情 ===")
    
    url = "https://**************/apps/secevent/attack_alarm/attack_alarm/get_attack_alarm_lists"
    
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "Cookie": "UEDC_LOGIN_PE_TIME=100000; UEDC_LOGIN_POLICY_VALUE=checked; sess_id=c72tunb0g",
        "Host": "**************",
        "Origin": "https://**************",
        "Referer": "https://**************/ui/",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "X-Requested-With": "XMLHttpRequest",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"'
    }
    
    payload = {
        "start": 0,
        "limit": 100,
        "host_type": ["all"],
        "new_alert_type": ["all"],
        "attack_result": ["all"],
        "reliability": [3, 2],
        "priority": [3, 2, 1],
        "stage": ["all"],
        "tag": ["all"],
        "status": ["all"],
        "http_status_code": ["all"],
        "attack_direction": ["all"],
        "src_branch_id": ["all"],
        "dst_branch_id": ["all"],
        "dev_id": ["all"],
        "is_read": "all",
        "branch_id": ["all"],
        "affect_type": ["all"],
        "time_range": "last24h",
        "custom_search_params": [],
        "page": 1,
        "only_focus_asset": 0,
        "view_branch_id": 0
    }
    
    print(f"目标URL: {url}")
    print(f"\n请求头数量: {len(headers)}")
    print("关键请求头:")
    print(f"  Cookie: {headers['Cookie']}")
    print(f"  User-Agent: {headers['User-Agent']}")
    print(f"  Origin: {headers['Origin']}")
    print(f"  Referer: {headers['Referer']}")
    
    payload_json = json.dumps(payload, ensure_ascii=False, indent=2)
    print(f"\n请求负载:")
    print(payload_json)
    print(f"\n负载大小: {len(payload_json.encode('utf-8'))} 字节")

if __name__ == "__main__":
    print("攻击告警数据提取工具 - 测试版本")
    
    # 显示请求详情
    show_request_details()
    
    # 测试数据提取功能
    test_data_extraction()
    
    print("\n=== 说明 ===")
    print("1. 上述测试验证了数据提取逻辑的正确性")
    print("2. 实际脚本 attack_alarm_request.py 已包含完整的请求头")
    print("3. 需要在能访问 ************** 的网络环境中运行")
    print("4. Cookie中的sess_id可能需要定期更新")
