import subprocess
import json
import os

def create_curl_command():
    """
    创建curl命令来发起POST请求
    """
    url = "https://**************/apps/secevent/attack_alarm/attack_alarm/get_attack_alarm_lists"
    
    # 请求负载
    payload = {
        "start": 0,
        "limit": 100,
        "host_type": ["all"],
        "new_alert_type": ["all"],
        "attack_result": ["all"],
        "reliability": [3, 2],
        "priority": [3, 2, 1],
        "stage": ["all"],
        "tag": ["all"],
        "status": ["all"],
        "http_status_code": ["all"],
        "attack_direction": ["all"],
        "src_branch_id": ["all"],
        "dst_branch_id": ["all"],
        "dev_id": ["all"],
        "is_read": "all",
        "branch_id": ["all"],
        "affect_type": ["all"],
        "time_range": "last24h",
        "custom_search_params": [],
        "page": 1,
        "only_focus_asset": 0,
        "view_branch_id": 0
    }
    
    # 将负载保存到临时文件
    payload_json = json.dumps(payload, ensure_ascii=False)
    with open('payload.json', 'w', encoding='utf-8') as f:
        f.write(payload_json)
    
    # 构建curl命令
    curl_cmd = [
        'curl',
        '-X', 'POST',
        '-k',  # 忽略SSL证书错误
        '--insecure',  # 允许不安全的SSL连接
        '--connect-timeout', '30',
        '--max-time', '60',
        '-H', 'Accept: application/json, text/plain, */*',
        '-H', 'Accept-Encoding: gzip, deflate, br, zstd',
        '-H', 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        '-H', 'Connection: keep-alive',
        '-H', 'Content-Type: application/json;charset=UTF-8',
        '-H', 'Cookie: UEDC_LOGIN_PE_TIME=100000; UEDC_LOGIN_POLICY_VALUE=checked; sess_id=c72tunb0g',
        '-H', 'Host: **************',
        '-H', 'Origin: https://**************',
        '-H', 'Referer: https://**************/ui/',
        '-H', 'Sec-Fetch-Dest: empty',
        '-H', 'Sec-Fetch-Mode: cors',
        '-H', 'Sec-Fetch-Site: same-origin',
        '-H', 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        '-H', 'X-Requested-With: XMLHttpRequest',
        '-H', 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        '-H', 'sec-ch-ua-mobile: ?0',
        '-H', 'sec-ch-ua-platform: "Windows"',
        '--data-binary', '@payload.json',
        url
    ]
    
    return curl_cmd

def run_curl_request():
    """
    使用curl发起请求
    """
    try:
        print("使用curl发起POST请求...")
        
        # 创建curl命令
        curl_cmd = create_curl_command()
        
        print("执行curl命令...")
        print(f"目标URL: https://**************/apps/secevent/attack_alarm/attack_alarm/get_attack_alarm_lists")
        
        # 执行curl命令
        result = subprocess.run(
            curl_cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=90
        )
        
        # 清理临时文件
        if os.path.exists('payload.json'):
            os.remove('payload.json')
        
        if result.returncode == 0:
            print("curl请求成功!")
            response_text = result.stdout
            
            try:
                # 解析JSON响应
                response_data = json.loads(response_text)
                print(f"响应总数: {response_data.get('total', 0)}")
                print(f"请求成功状态: {response_data.get('success', False)}")
                
                # 提取data字段
                data = response_data.get('data', [])
                
                # 如果data是字符串，尝试解析为JSON
                if isinstance(data, str):
                    try:
                        data = json.loads(data)
                    except json.JSONDecodeError:
                        print("警告: data字段不是有效的JSON格式")
                        return []
                
                # 提取attack_ip属性
                attack_ips = []
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict) and 'attack_ip' in item:
                            attack_ips.append(item['attack_ip'])
                
                print(f"成功提取到 {len(attack_ips)} 个attack_ip")
                return attack_ips
                
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                print(f"原始响应: {response_text[:500]}...")
                return []
                
        else:
            print(f"curl请求失败，返回码: {result.returncode}")
            print(f"错误信息: {result.stderr}")
            return []
            
    except subprocess.TimeoutExpired:
        print("请求超时")
        return []
    except FileNotFoundError:
        print("错误: 未找到curl命令")
        print("请安装curl或使用其他方法")
        return []
    except Exception as e:
        print(f"未知错误: {e}")
        return []

def generate_curl_command_file():
    """
    生成可以手动执行的curl命令文件
    """
    curl_cmd = create_curl_command()
    
    # 生成Windows批处理文件
    with open('run_curl.bat', 'w', encoding='utf-8') as f:
        f.write('@echo off\n')
        f.write('echo 正在发起POST请求...\n')
        f.write(' '.join(f'"{arg}"' if ' ' in arg else arg for arg in curl_cmd))
        f.write('\npause\n')
    
    # 生成PowerShell脚本
    with open('run_curl.ps1', 'w', encoding='utf-8') as f:
        f.write('Write-Host "正在发起POST请求..."\n')
        f.write('$response = ')
        f.write(' '.join(f'"{arg}"' if ' ' in arg else arg for arg in curl_cmd))
        f.write('\nWrite-Host $response\n')
        f.write('Read-Host "按Enter键退出"\n')
    
    print("已生成curl命令文件:")
    print("- run_curl.bat (Windows批处理)")
    print("- run_curl.ps1 (PowerShell脚本)")

def main():
    """
    主函数
    """
    print("攻击告警数据提取工具 - curl版本")
    print("开始获取攻击告警数据...")
    
    # 生成curl命令文件
    generate_curl_command_file()
    
    # 尝试运行curl
    attack_ips = run_curl_request()
    
    if attack_ips:
        print("\n提取到的attack_ip列表:")
        for i, ip in enumerate(attack_ips, 1):
            print(f"{i}. {ip}")
        
        # 保存到文件
        with open('attack_ips.txt', 'w', encoding='utf-8') as f:
            for ip in attack_ips:
                f.write(f"{ip}\n")
        print(f"\nattack_ip列表已保存到 attack_ips.txt 文件")
        
        # 统计唯一IP
        unique_ips = list(set(attack_ips))
        print(f"\n统计信息:")
        print(f"总计attack_ip数量: {len(attack_ips)}")
        print(f"唯一attack_ip数量: {len(unique_ips)}")
        
    else:
        print("未能提取到任何attack_ip数据")
        print("\n如果curl不可用，您可以:")
        print("1. 手动运行生成的 run_curl.bat 文件")
        print("2. 在PowerShell中运行 run_curl.ps1")
        print("3. 安装curl工具后重新运行此脚本")

if __name__ == "__main__":
    main()
