import urllib.request
import urllib.parse
import json
import ssl
import gzip
import io

def get_login_info():
    """
    向登录API发起POST请求获取登录信息
    """
    # API端点
    url = "https://********:8443/rest/login"
    
    # 请求头
    headers = {
        "Accept": "*/*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "text/plain;charset=UTF-8",
        "Host": "********:8443",
        "Origin": "https://********:8443",
        "Referer": "https://********:8443/",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "X-API-Language": "zh_CN",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"'
    }
    
    # 请求负载
    payload = {
        "encodeUserName": True,
        "encodePassword": True,
        "userName": "YWRkYm9vaw==",
        "password": "JGpjVV85MEU=",
        "captcha": False,
        "lang": "zh_CN",
        "requestType": 10,
        "admin_login_id": ""
    }
    
    try:
        # 创建SSL上下文，跳过证书验证
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        ssl_context.set_ciphers('DEFAULT:@SECLEVEL=1')
        
        # 准备POST数据
        post_data = json.dumps(payload).encode('utf-8')
        
        # 创建请求对象
        req = urllib.request.Request(url, data=post_data, headers=headers, method='POST')
        
        # 发起POST请求
        print("正在发起登录请求...")
        print(f"目标URL: {url}")
        print(f"负载大小: {len(post_data)} 字节")
        print(f"用户名(编码): {payload['userName']}")
        
        with urllib.request.urlopen(req, context=ssl_context, timeout=30) as response:
            # 读取响应
            response_bytes = response.read()
            status_code = response.getcode()
            content_encoding = response.getheader('Content-Encoding', '')
            
            print(f"请求成功，状态码: {status_code}")
            print(f"Content-Encoding: {content_encoding}")
            print(f"响应大小: {len(response_bytes)} 字节")
            
            # 智能解码响应
            try:
                if response_bytes.startswith(b'\x1f\x8b') or content_encoding == 'gzip':
                    print("检测到gzip压缩响应，正在解压...")
                    response_body = gzip.decompress(response_bytes).decode('utf-8')
                elif content_encoding == 'deflate':
                    print("检测到deflate压缩响应，正在解压...")
                    import zlib
                    response_body = zlib.decompress(response_bytes).decode('utf-8')
                else:
                    response_body = response_bytes.decode('utf-8')
            except Exception as decode_error:
                print(f"响应解码错误: {decode_error}")
                # 尝试其他编码
                try:
                    response_body = response_bytes.decode('gbk')
                    print("使用GBK编码成功解码")
                except:
                    print("无法解码响应，显示原始字节...")
                    print(f"响应前200字节: {response_bytes[:200]}")
                    return None
            
            print(f"响应长度: {len(response_body)} 字符")
            
            # 解析JSON响应
            try:
                response_data = json.loads(response_body)
                print("✓ JSON解析成功")
                
                # 显示响应结构
                print(f"success: {response_data.get('success', 'N/A')}")
                print(f"total: {response_data.get('total', 'N/A')}")
                
                # 检查result字段
                result = response_data.get('result', {})
                if result:
                    print(f"result字段类型: {type(result)}")
                    if isinstance(result, dict):
                        print(f"result包含 {len(result)} 个字段")
                        print("result字段列表:", list(result.keys())[:10])  # 显示前10个字段
                    else:
                        print(f"result内容: {str(result)[:200]}...")
                
                return response_data
                
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                print(f"响应内容前500字符: {response_body[:500]}")
                return None
        
    except urllib.error.HTTPError as e:
        print(f"HTTP错误: {e.code} - {e.reason}")
        if hasattr(e, 'read'):
            try:
                error_body = e.read().decode('utf-8')
                print(f"错误详情: {error_body}")
            except:
                print("无法读取错误详情")
        return None
    except urllib.error.URLError as e:
        print(f"URL错误: {e.reason}")
        return None
    except ssl.SSLError as e:
        print(f"SSL错误: {e}")
        return None
    except Exception as e:
        print(f"未知错误: {e}")
        return None

def save_login_response(response_data):
    """
    保存登录响应到文件
    """
    if response_data:
        # 保存完整响应
        with open('login_response.json', 'w', encoding='utf-8') as f:
            json.dump(response_data, f, ensure_ascii=False, indent=2)
        print("✓ 完整响应已保存到 login_response.json")
        
        # 单独保存result字段
        result = response_data.get('result', {})
        if result:
            with open('login_result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print("✓ result字段已保存到 login_result.json")
        
        # 创建摘要信息
        summary = {
            "登录状态": "成功" if response_data.get('success') else "失败",
            "响应时间": "获取成功",
            "result字段数量": len(result) if isinstance(result, dict) else "非字典类型",
            "total": response_data.get('total', 'N/A')
        }
        
        with open('login_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        print("✓ 登录摘要已保存到 login_summary.json")

def test_connection():
    """
    测试与目标服务器的连接
    """
    import socket
    
    host = "********"
    port = 8443
    
    print(f"测试连接到 {host}:{port}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print("✓ 网络连接正常")
            return True
        else:
            print("✗ 无法连接到目标服务器")
            return False
    except Exception as e:
        print(f"✗ 连接测试失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("登录信息获取工具")
    print("=" * 50)
    
    # 测试网络连接
    if not test_connection():
        print("\n请检查:")
        print("1. 网络连接是否正常")
        print("2. 是否在正确的网络环境中")
        print("3. 目标服务器 ********:8443 是否可访问")
        return
    
    # 发起登录请求
    print("\n开始获取登录信息...")
    response_data = get_login_info()
    
    if response_data:
        print("\n" + "=" * 50)
        print("🎉 登录请求成功！")
        print("=" * 50)
        
        # 显示关键信息
        print(f"登录状态: {'成功' if response_data.get('success') else '失败'}")
        print(f"总计数量: {response_data.get('total', 'N/A')}")
        
        result = response_data.get('result', {})
        if isinstance(result, dict):
            print(f"result字段包含: {len(result)} 个属性")
            # 显示result的前几个字段作为预览
            preview_keys = list(result.keys())[:5]
            if preview_keys:
                print("result字段预览:")
                for key in preview_keys:
                    value = result[key]
                    if isinstance(value, str) and len(value) > 50:
                        print(f"  {key}: {value[:50]}...")
                    else:
                        print(f"  {key}: {value}")
        
        # 保存响应数据
        save_login_response(response_data)
        
        print("\n文件输出:")
        print("- login_response.json: 完整响应数据")
        print("- login_result.json: result字段数据")
        print("- login_summary.json: 登录摘要信息")
        
    else:
        print("\n❌ 登录请求失败")
        print("请检查网络连接、服务器状态或认证信息")

if __name__ == "__main__":
    main()
