import urllib.request
import urllib.parse
import json
import ssl
import gzip
import socket
import struct

def ip_to_decimal(ip_str):
    """
    将IP地址字符串转换为10进制数
    """
    if len(ip_str) != 0:
        try:
            return struct.unpack("!I", socket.inet_aton(ip_str))[0]
        except socket.error:
            print(f"警告: 无效的IP地址 {ip_str}")
            return None

def get_login_info():
    """
    获取登录信息
    """
    url = "https://********:8443/rest/login"
    
    headers = {
        "Accept": "*/*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "text/plain;charset=UTF-8",
        "Host": "********:8443",
        "Origin": "https://********:8443",
        "Referer": "https://********:8443/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "X-API-Language": "zh_CN"
    }
    
    payload = {
        "encodeUserName": True,
        "encodePassword": True,
        "userName": "YWRkYm9vaw==",
        "password": "JGpjVV85MEU=",
        "captcha": False,
        "lang": "zh_CN",
        "requestType": 10,
        "admin_login_id": ""
    }
    
    try:
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        ssl_context.set_ciphers('DEFAULT:@SECLEVEL=1')
        
        post_data = json.dumps(payload).encode('utf-8')
        req = urllib.request.Request(url, data=post_data, headers=headers, method='POST')
        
        print("正在获取登录信息...")
        with urllib.request.urlopen(req, context=ssl_context, timeout=30) as response:
            response_bytes = response.read()
            
            # 处理gzip压缩
            if response_bytes.startswith(b'\x1f\x8b'):
                response_body = gzip.decompress(response_bytes).decode('utf-8')
            else:
                response_body = response_bytes.decode('utf-8')
            
            response_data = json.loads(response_body)
            
            if response_data.get('success'):
                print("✓ 登录成功")
                return response_data.get('result', {})
            else:
                print("✗ 登录失败")
                return None
                
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def get_attack_alarm_data(auth_token=None, cookies=None):
    """
    获取攻击告警数据中的IP地址
    """
    url = "https://**************/apps/secevent/attack_alarm/attack_alarm/get_attack_alarm_lists"
    
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "Cookie": "UEDC_LOGIN_PE_TIME=100000; UEDC_LOGIN_POLICY_VALUE=checked; sess_id=c72tunb0g",
        "Host": "**************",
        "Origin": "https://**************",
        "Referer": "https://**************/ui/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    }
    
    payload = {
        "start": 0,
        "limit": 100,
        "host_type": ["all"],
        "new_alert_type": ["all"],
        "attack_result": ["all"],
        "reliability": [3, 2],
        "priority": [3, 2, 1],
        "stage": ["all"],
        "tag": ["all"],
        "status": ["all"],
        "http_status_code": ["all"],
        "attack_direction": ["all"],
        "src_branch_id": ["all"],
        "dst_branch_id": ["all"],
        "dev_id": ["all"],
        "is_read": "all",
        "branch_id": ["all"],
        "affect_type": ["all"],
        "time_range": "last24h",
        "custom_search_params": [],
        "page": 1,
        "only_focus_asset": 0,
        "view_branch_id": 0
    }
    
    try:
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        ssl_context.set_ciphers('DEFAULT:@SECLEVEL=1')
        
        post_data = json.dumps(payload).encode('utf-8')
        req = urllib.request.Request(url, data=post_data, headers=headers, method='POST')
        
        print("正在获取攻击告警数据...")
        with urllib.request.urlopen(req, context=ssl_context, timeout=30) as response:
            response_bytes = response.read()
            
            # 处理gzip压缩
            if response_bytes.startswith(b'\x1f\x8b'):
                response_body = gzip.decompress(response_bytes).decode('utf-8')
            else:
                response_body = response_bytes.decode('utf-8')
            
            response_data = json.loads(response_body)
            
            # 提取attack_ip
            data = response_data.get('data', [])
            if isinstance(data, str):
                data = json.loads(data)
            
            attack_ips = []
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict) and 'attack_ip' in item:
                        attack_ips.append(item['attack_ip'])
            
            print(f"✓ 获取到 {len(attack_ips)} 个攻击IP")
            return attack_ips
            
    except Exception as e:
        print(f"获取攻击告警数据失败: {e}")
        return []

def construct_cookie_from_login_data(login_data):
    """
    从登录数据构造Cookie字符串
    """
    if not login_data or 'data' not in login_data:
        print("警告: 登录数据中没有找到data字段")
        return ""

    data = login_data['data']
    cookie_parts = []

    # 遍历data中的所有属性构造cookie
    for key, value in data.items():
        if isinstance(value, (str, int, float)):
            # 对包含中文或特殊字符的值进行URL编码
            if isinstance(value, str):
                # 检查是否包含非ASCII字符
                try:
                    value.encode('ascii')
                    # 如果没有异常，说明是纯ASCII，直接使用
                    cookie_parts.append(f"{key}={value}")
                except UnicodeEncodeError:
                    # 包含非ASCII字符，进行URL编码
                    import urllib.parse
                    encoded_value = urllib.parse.quote(str(value), safe='')
                    cookie_parts.append(f"{key}={encoded_value}")
            else:
                cookie_parts.append(f"{key}={value}")

    # 添加一些必要的cookie字段
    essential_cookies = [
        "vsysName=root",
        "fromrootvsys=true",
        "zoneinfo=Asia%2FChongqing"
    ]

    cookie_parts.extend(essential_cookies)
    cookie_string = "; ".join(cookie_parts)

    print(f"构造的Cookie长度: {len(cookie_string)} 字符")
    return cookie_string

def send_address_book_request(login_data, attack_ips):
    """
    发送地址簿PUT请求
    """
    url = "https://********:8443/rest/addrbook_address?isTransaction=1&idfield=is_ipv6%2Ctype%2Cname"
    
    # 从登录数据构造Cookie
    cookie_string = construct_cookie_from_login_data(login_data)
    
    # 从登录数据获取token
    token = ""
    if login_data and 'data' in login_data:
        token = login_data['data'].get('token', '')
    
    headers = {
        "Accept": "*/*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "Cookie": cookie_string,
        "Host": "********:8443",
        "Referer": "https://********:8443/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "X-API-Language": "zh_CN",
        "X-API-Version": "1",
        "X-Auth-Token": token,
        "X-Requested-With": "XMLHttpRequest"
    }
    
    # 构造IP列表
    ip_list = []
    for ip_str in attack_ips:
        ip_decimal = ip_to_decimal(ip_str)
        if ip_decimal is not None:
            ip_list.append({
                "ip_addr": ip_decimal,
                "netmask": 32,
                "flag": 0
            })
    
    # 构造负载
    payload = [{
        "is_ipv6": "0",
        "type": "0",
        "name": "地址簿08",
        "description": "api封禁",
        "entry": [],
        "ip": ip_list,
        "range": [],
        "host": [],
        "wildcard": [],
        "country": []
    }]
    
    try:
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        ssl_context.set_ciphers('DEFAULT:@SECLEVEL=1')
        
        post_data = json.dumps(payload, ensure_ascii=False).encode('utf-8')
        req = urllib.request.Request(url, data=post_data, headers=headers, method='PUT')
        
        print(f"正在发送地址簿PUT请求...")
        print(f"包含 {len(ip_list)} 个IP地址")
        print(f"负载大小: {len(post_data)} 字节")
        
        with urllib.request.urlopen(req, context=ssl_context, timeout=30) as response:
            response_bytes = response.read()
            status_code = response.getcode()
            
            # 处理gzip压缩
            if response_bytes.startswith(b'\x1f\x8b'):
                response_body = gzip.decompress(response_bytes).decode('utf-8')
            else:
                response_body = response_bytes.decode('utf-8')
            
            print(f"✓ 请求成功，状态码: {status_code}")
            
            try:
                response_data = json.loads(response_body)
                print(f"响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                return response_data
            except json.JSONDecodeError:
                print(f"响应内容: {response_body}")
                return {"raw_response": response_body}
                
    except Exception as e:
        print(f"地址簿请求失败: {e}")
        return None

def main():
    """
    主函数
    """
    print("地址簿操作工具")
    print("=" * 50)
    
    # 1. 获取登录信息
    login_data = get_login_info()
    if not login_data:
        print("❌ 无法获取登录信息，程序退出")
        return
    
    # 2. 获取攻击IP列表
    attack_ips = get_attack_alarm_data()
    if not attack_ips:
        print("❌ 无法获取攻击IP数据，程序退出")
        return
    
    print(f"获取到的攻击IP示例: {attack_ips[:5]}")
    
    # 3. 发送地址簿请求
    result = send_address_book_request(login_data, attack_ips)
    
    if result:
        print("\n🎉 地址簿操作完成！")
        
        # 保存结果
        with open('address_book_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print("结果已保存到 address_book_result.json")
        
    else:
        print("\n❌ 地址簿操作失败")

if __name__ == "__main__":
    main()
