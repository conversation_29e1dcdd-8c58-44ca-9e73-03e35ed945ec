import urllib.request
import urllib.parse
import json
import ssl
import gzip
import base64

def encode_credentials(username, password):
    """
    对用户名和密码进行Base64编码
    
    Args:
        username (str): 用户名
        password (str): 密码
    
    Returns:
        tuple: (编码后的用户名, 编码后的密码)
    """
    encoded_username = base64.b64encode(username.encode('utf-8')).decode('utf-8')
    encoded_password = base64.b64encode(password.encode('utf-8')).decode('utf-8')
    return encoded_username, encoded_password

def login_and_get_token(username="addbook", password="$jcU_90E"):
    """
    登录系统并获取token
    
    Args:
        username (str): 用户名，默认为"addbook"
        password (str): 密码，默认为"$jcU_90E"
    
    Returns:
        dict: 登录响应数据，包含token等信息
    """
    # API端点
    url = "https://********:8443/rest/login"
    
    # 请求头
    headers = {
        "Accept": "*/*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "text/plain;charset=UTF-8",
        "Host": "********:8443",
        "Origin": "https://********:8443",
        "Referer": "https://********:8443/",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "X-API-Language": "zh_CN",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"'
    }
    
    # 对用户名和密码进行Base64编码
    encoded_username, encoded_password = encode_credentials(username, password)
    
    # 请求负载
    payload = {
        "encodeUserName": True,
        "encodePassword": True,
        "userName": encoded_username,
        "password": encoded_password,
        "captcha": False,
        "lang": "zh_CN",
        "requestType": 10,
        "admin_login_id": ""
    }
    
    try:
        # 创建SSL上下文，跳过证书验证
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        ssl_context.set_ciphers('DEFAULT:@SECLEVEL=1')
        
        # 准备POST数据
        post_data = json.dumps(payload).encode('utf-8')
        
        # 更新Content-Length头
        headers["Content-Length"] = str(len(post_data))
        
        # 创建请求对象
        req = urllib.request.Request(url, data=post_data, headers=headers, method='POST')
        
        # 发起POST请求
        print("正在发起登录请求...")
        print(f"目标URL: {url}")
        print(f"用户名: {username} (编码后: {encoded_username})")
        print(f"负载大小: {len(post_data)} 字节")
        
        with urllib.request.urlopen(req, context=ssl_context, timeout=30) as response:
            # 读取响应
            response_bytes = response.read()
            status_code = response.getcode()
            content_encoding = response.getheader('Content-Encoding', '')
            
            print(f"请求成功，状态码: {status_code}")
            print(f"Content-Encoding: {content_encoding}")
            
            # 智能解码响应
            try:
                if response_bytes.startswith(b'\x1f\x8b') or content_encoding == 'gzip':
                    print("检测到gzip压缩响应，正在解压...")
                    response_body = gzip.decompress(response_bytes).decode('utf-8')
                elif content_encoding == 'deflate':
                    print("检测到deflate压缩响应，正在解压...")
                    import zlib
                    response_body = zlib.decompress(response_bytes).decode('utf-8')
                else:
                    response_body = response_bytes.decode('utf-8')
            except Exception as decode_error:
                print(f"响应解码错误: {decode_error}")
                # 尝试其他编码
                try:
                    response_body = response_bytes.decode('gbk')
                    print("使用GBK编码成功解码")
                except:
                    print("无法解码响应")
                    return None
            
            print(f"响应长度: {len(response_body)} 字符")
            
            # 解析JSON响应
            response_data = json.loads(response_body)
            print(f"登录成功状态: {response_data.get('success', False)}")
            print(f"响应总数: {response_data.get('total', 0)}")
            
            # 显示响应结构
            if 'result' in response_data:
                result = response_data['result']
                print(f"result字段类型: {type(result)}")
                if isinstance(result, dict):
                    print(f"result包含的键: {list(result.keys())}")
                    
                    # 查找可能的token字段
                    token_fields = ['token', 'access_token', 'auth_token', 'sessionId', 'session_id']
                    found_token = None
                    for field in token_fields:
                        if field in result:
                            found_token = result[field]
                            print(f"找到token字段 '{field}': {found_token}")
                            break
                    
                    if not found_token:
                        print("未找到明显的token字段，显示result内容:")
                        print(json.dumps(result, indent=2, ensure_ascii=False))
            
            return response_data
        
    except urllib.error.HTTPError as e:
        print(f"HTTP错误: {e.code} - {e.reason}")
        if hasattr(e, 'read'):
            try:
                error_body = e.read().decode('utf-8')
                print(f"错误详情: {error_body}")
            except:
                pass
        return None
    except urllib.error.URLError as e:
        print(f"URL错误: {e.reason}")
        return None
    except ssl.SSLError as e:
        print(f"SSL错误: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        print(f"原始响应: {response_body[:500]}...")
        return None
    except Exception as e:
        print(f"未知错误: {e}")
        return None

def extract_token_from_response(response_data):
    """
    从登录响应中提取token
    
    Args:
        response_data (dict): 登录响应数据
    
    Returns:
        str: 提取到的token，如果未找到则返回None
    """
    if not response_data or not response_data.get('success'):
        print("登录失败或响应无效")
        return None
    
    result = response_data.get('result', {})
    if not isinstance(result, dict):
        print("result字段格式不正确")
        return None
    
    # 常见的token字段名
    token_fields = [
        'token', 'access_token', 'auth_token', 'authToken',
        'sessionId', 'session_id', 'sessionToken', 'jwt',
        'bearer_token', 'api_token'
    ]
    
    for field in token_fields:
        if field in result and result[field]:
            print(f"成功提取token (字段: {field})")
            return result[field]
    
    print("未找到token字段，可能需要手动查找")
    return None

def save_login_info(response_data, filename="login_info.json"):
    """
    保存登录信息到文件
    
    Args:
        response_data (dict): 登录响应数据
        filename (str): 保存的文件名
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(response_data, f, indent=2, ensure_ascii=False)
        print(f"登录信息已保存到 {filename}")
    except Exception as e:
        print(f"保存文件失败: {e}")

def main():
    """
    主函数
    """
    print("登录获取Token工具")
    print("=" * 50)
    
    # 可以在这里修改用户名和密码
    username = "addbook"  # 对应编码后的 YWRkYm9vaw==
    password = "$jcU_90E"  # 对应编码后的 JGpjVV85MEU=
    
    print(f"使用用户名: {username}")
    print(f"使用密码: {password}")
    
    # 发起登录请求
    response_data = login_and_get_token(username, password)
    
    if response_data:
        print("\n" + "=" * 50)
        print("登录响应:")
        print(json.dumps(response_data, indent=2, ensure_ascii=False))
        
        # 尝试提取token
        token = extract_token_from_response(response_data)
        if token:
            print(f"\n🎉 成功获取Token: {token}")
            
            # 保存token到单独文件
            with open('token.txt', 'w', encoding='utf-8') as f:
                f.write(token)
            print("Token已保存到 token.txt 文件")
        else:
            print("\n⚠️ 未能自动提取token，请手动查看响应数据")
        
        # 保存完整登录信息
        save_login_info(response_data)
        
    else:
        print("\n❌ 登录失败")

if __name__ == "__main__":
    main()
