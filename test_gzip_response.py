import urllib.request
import urllib.parse
import json
import ssl
import gzip
import io

def test_gzip_handling():
    """
    测试gzip响应处理
    """
    print("=== Gzip响应处理测试 ===")
    
    # 模拟gzip压缩的响应数据
    test_data = {
        "total": 2,
        "success": True,
        "data": [
            {"attack_ip": "*************", "attack_type": "测试攻击1"},
            {"attack_ip": "*********", "attack_type": "测试攻击2"}
        ]
    }
    
    # 将测试数据转换为JSON并压缩
    json_data = json.dumps(test_data, ensure_ascii=False).encode('utf-8')
    compressed_data = gzip.compress(json_data)
    
    print(f"原始JSON大小: {len(json_data)} 字节")
    print(f"压缩后大小: {len(compressed_data)} 字节")
    print(f"压缩比: {len(compressed_data)/len(json_data)*100:.1f}%")
    
    # 测试解压缩
    try:
        if compressed_data.startswith(b'\x1f\x8b'):  # gzip魔数
            print("✓ 检测到gzip压缩数据")
            decompressed_data = gzip.decompress(compressed_data).decode('utf-8')
            print("✓ 成功解压缩")
            
            # 解析JSON
            parsed_data = json.loads(decompressed_data)
            print("✓ 成功解析JSON")
            
            # 提取attack_ip
            attack_ips = []
            for item in parsed_data.get('data', []):
                if 'attack_ip' in item:
                    attack_ips.append(item['attack_ip'])
            
            print(f"✓ 成功提取 {len(attack_ips)} 个attack_ip: {attack_ips}")
            
        else:
            print("✗ 未检测到gzip压缩")
            
    except Exception as e:
        print(f"✗ 处理失败: {e}")

def make_real_request():
    """
    发起真实请求测试
    """
    print("\n=== 真实请求测试 ===")
    
    url = "https://**************/apps/secevent/attack_alarm/attack_alarm/get_attack_alarm_lists"
    
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "Cookie": "UEDC_LOGIN_PE_TIME=100000; UEDC_LOGIN_POLICY_VALUE=checked; sess_id=c72tunb0g",
        "Host": "**************",
        "Origin": "https://**************",
        "Referer": "https://**************/ui/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    }
    
    payload = {
        "start": 0,
        "limit": 100,
        "host_type": ["all"],
        "new_alert_type": ["all"],
        "attack_result": ["all"],
        "reliability": [3, 2],
        "priority": [3, 2, 1],
        "stage": ["all"],
        "tag": ["all"],
        "status": ["all"],
        "http_status_code": ["all"],
        "attack_direction": ["all"],
        "src_branch_id": ["all"],
        "dst_branch_id": ["all"],
        "dev_id": ["all"],
        "is_read": "all",
        "branch_id": ["all"],
        "affect_type": ["all"],
        "time_range": "last24h",
        "custom_search_params": [],
        "page": 1,
        "only_focus_asset": 0,
        "view_branch_id": 0
    }
    
    try:
        # 创建SSL上下文
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        ssl_context.set_ciphers('DEFAULT:@SECLEVEL=1')
        
        # 准备POST数据
        post_data = json.dumps(payload).encode('utf-8')
        req = urllib.request.Request(url, data=post_data, headers=headers, method='POST')
        
        print("正在发起请求...")
        
        with urllib.request.urlopen(req, context=ssl_context, timeout=30) as response:
            response_bytes = response.read()
            status_code = response.getcode()
            content_encoding = response.getheader('Content-Encoding', '')
            
            print(f"状态码: {status_code}")
            print(f"Content-Encoding: {content_encoding}")
            print(f"响应大小: {len(response_bytes)} 字节")
            print(f"响应前20字节: {response_bytes[:20]}")
            
            # 智能解码响应
            try:
                if response_bytes.startswith(b'\x1f\x8b') or content_encoding == 'gzip':
                    print("✓ 检测到gzip压缩，正在解压...")
                    response_body = gzip.decompress(response_bytes).decode('utf-8')
                elif content_encoding == 'deflate':
                    print("✓ 检测到deflate压缩，正在解压...")
                    import zlib
                    response_body = zlib.decompress(response_bytes).decode('utf-8')
                else:
                    print("✓ 使用UTF-8直接解码...")
                    response_body = response_bytes.decode('utf-8')
                
                print(f"解码后长度: {len(response_body)} 字符")
                print(f"响应前200字符: {response_body[:200]}...")
                
                # 解析JSON
                response_data = json.loads(response_body)
                print(f"✓ JSON解析成功")
                print(f"total: {response_data.get('total', 'N/A')}")
                print(f"success: {response_data.get('success', 'N/A')}")
                
                # 提取attack_ip
                data = response_data.get('data', [])
                if isinstance(data, str):
                    data = json.loads(data)
                
                attack_ips = []
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict) and 'attack_ip' in item:
                            attack_ips.append(item['attack_ip'])
                
                print(f"✓ 成功提取 {len(attack_ips)} 个attack_ip")
                if attack_ips:
                    print("前5个IP:", attack_ips[:5])
                
                return attack_ips
                
            except Exception as decode_error:
                print(f"✗ 解码失败: {decode_error}")
                return []
                
    except Exception as e:
        print(f"✗ 请求失败: {e}")
        return []

if __name__ == "__main__":
    print("Gzip响应处理测试工具")
    
    # 测试gzip处理逻辑
    test_gzip_handling()
    
    # 尝试真实请求
    attack_ips = make_real_request()
    
    if attack_ips:
        print(f"\n🎉 成功！提取到 {len(attack_ips)} 个attack_ip")
        with open('attack_ips_test.txt', 'w', encoding='utf-8') as f:
            for ip in attack_ips:
                f.write(f"{ip}\n")
        print("结果已保存到 attack_ips_test.txt")
    else:
        print("\n❌ 未能提取到数据")
