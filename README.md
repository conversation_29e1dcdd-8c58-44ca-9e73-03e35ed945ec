# 攻击告警数据提取工具

这个Python脚本用于从攻击告警API提取attack_ip数据。

## 功能特性

- 发起POST请求到指定的攻击告警API
- 提取响应中的attack_ip属性
- 支持SSL证书跳过（适用于内网环境）
- 包含网络连接测试功能
- 支持认证token和cookies
- 自动保存结果到文件
- 提供详细的统计信息

## 使用方法

### 基本使用
```bash
python attack_alarm_request.py
```

### 如果需要认证
在脚本的main()函数中取消注释并设置：
```python
auth_token = "your_token_here"
cookies = "session_id=xxx; csrf_token=yyy"
attack_ips = get_attack_alarm_data(auth_token=auth_token, cookies=cookies)
```

## 网络要求

- 需要能够访问内网地址 `**************`
- 可能需要VPN连接或特定网络环境
- 需要HTTPS端口443可访问

## 输出文件

- `attack_ips.txt`: 包含所有提取的attack_ip地址

## 故障排除

1. **连接失败**: 检查网络连接和VPN状态
2. **认证错误**: 添加必要的token或cookies
3. **SSL错误**: 脚本已配置跳过SSL验证

## 脚本结构

- `test_connection()`: 测试网络连接
- `get_attack_alarm_data()`: 主要的API请求函数
- `main()`: 主函数，协调整个流程

## 注意事项

- 脚本使用Python标准库，无需额外依赖
- 适用于内网环境的安全测试
- 请确保有适当的权限访问目标API
