curl -X PUT \
  "https://********:8443/rest/addrbook_address?isTransaction=1&idfield=is_ipv6%2Ctype%2Cname" \
  -H "Accept: */*" \
  -H "Accept-Encoding: gzip, deflate, br, zstd" \
  -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" \
  -H "Connection: keep-alive" \
  -H "Content-Type: application/json;charset=UTF-8" \
  -H "Cookie: PHPSESSID=i9cnjk2b8tgk9pnb0jivmltfls; uuid=e6d8ea61-1c6c-7849-ad18-093e5361f433; role=api地址簿接口; username=addbook; vsysId=0; token=CzZe6NAwPupfeA9xfmHIn57NJjSJ1IEK4ShL6K4; user_id=12345; session_timeout=3600; vsysName=root; fromrootvsys=true; zoneinfo=Asia%2FChongqing" \
  -H "Host: ********:8443" \
  -H "Referer: https://********:8443/" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" \
  -H "X-API-Language: zh_CN" \
  -H "X-API-Version: 1" \
  -H "X-Auth-Token: CzZe6NAwPupfeA9xfmHIn57NJjSJ1IEK4ShL6K4" \
  -H "X-Requested-With: XMLHttpRequest" \
  -k --insecure \
  --data-binary @address_book_payload.json