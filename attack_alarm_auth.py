import urllib.request
import urllib.parse
import json
import ssl
import gzip
import time
import base64
import io

try:
    import ddddocr
    DDDDOCR_AVAILABLE = True
except ImportError:
    DDDDOCR_AVAILABLE = False
    print("警告: ddddocr库未安装，验证码识别功能不可用")
    print("请运行: pip install ddddocr")

def get_captcha_image(session_id=None):
    """
    获取验证码图片的base64编码
    """
    # 生成当前毫秒级时间戳
    timestamp = int(time.time() * 1000)
    url = f"https://**************/apps/secvisual/auth_manage/auth_manage/req_captcha?{timestamp}"
    
    # 请求头
    headers = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Cookie": f"UEDC_LOGIN_POLICY_UPDATE_TIME=1621468800000; UEDC_LOGIN_POLICY_VALUE=checked; sess_id={session_id or 'rak4829uonl9qvbgua9uibrrbte7cfjn'}",
        "Host": "**************",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"'
    }
    
    try:
        # 创建SSL上下文
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        ssl_context.set_ciphers('DEFAULT:@SECLEVEL=1')
        
        # 创建请求
        req = urllib.request.Request(url, headers=headers, method='GET')
        
        print(f"正在获取验证码图片... (毫秒时间戳: {timestamp})")
        
        with urllib.request.urlopen(req, context=ssl_context, timeout=30) as response:
            response_bytes = response.read()
            status_code = response.getcode()
            content_type = response.getheader('Content-Type', '')
            
            print(f"验证码请求状态码: {status_code}")
            print(f"Content-Type: {content_type}")
            
            # 检查是否是图片
            if 'image' in content_type:
                # 将图片转换为base64
                captcha_base64 = base64.b64encode(response_bytes).decode('utf-8')
                print(f"✓ 获取验证码图片成功，大小: {len(response_bytes)} 字节")
                return captcha_base64, response_bytes
            else:
                # 可能是HTML或其他格式，尝试解码
                if response_bytes.startswith(b'\x1f\x8b'):
                    response_body = gzip.decompress(response_bytes).decode('utf-8')
                else:
                    response_body = response_bytes.decode('utf-8')
                
                print(f"警告: 响应不是图片格式")
                print(f"响应内容前200字符: {response_body[:200]}")
                return None, None
                
    except Exception as e:
        print(f"获取验证码失败: {e}")
        return None, None

def recognize_captcha(image_bytes, manual_input=False):
    """
    识别验证码 - 支持自动识别和手动输入
    """
    if DDDDOCR_AVAILABLE and not manual_input:
        try:
            # 初始化OCR
            ocr = ddddocr.DdddOcr()

            # 识别验证码
            captcha_text = ocr.classification(image_bytes)
            print(f"✓ 自动识别验证码: {captcha_text}")

            return captcha_text

        except Exception as e:
            print(f"自动识别失败: {e}")
            print("切换到手动输入模式...")
            manual_input = True

    if manual_input or not DDDDOCR_AVAILABLE:
        # 保存验证码图片供用户查看
        with open('current_captcha.png', 'wb') as f:
            f.write(image_bytes)
        print("✓ 验证码图片已保存到 current_captcha.png")
        print("请打开图片查看验证码，然后手动输入...")

        # 手动输入验证码
        try:
            captcha_text = input("请输入验证码: ").strip()
            if captcha_text:
                print(f"✓ 手动输入验证码: {captcha_text}")
                return captcha_text
            else:
                print("❌ 验证码不能为空")
                return None
        except KeyboardInterrupt:
            print("\n❌ 用户取消输入")
            return None

    return None

def login_attack_alarm_system(username="xiaoan", password="XiaoAn2024", session_id=None, manual_captcha=False):
    """
    登录攻击告警系统
    """
    print("开始登录攻击告警系统...")

    # 1. 获取验证码
    captcha_base64, captcha_bytes = get_captcha_image(session_id)
    if not captcha_bytes:
        print("❌ 无法获取验证码")
        return None

    # 2. 识别验证码
    captcha_text = recognize_captcha(captcha_bytes, manual_input=manual_captcha)
    if not captcha_text:
        print("❌ 无法识别验证码")
        return None
    
    # 3. 发起登录请求
    url = "https://**************/apps/secvisual/auth_manage/Auth_manage/on_login"
    
    headers = {
        "Accept": "text/html, */*; q=0.01",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Cookie": f"sess_id={session_id or 't8dg4acjlf64g6v9b1tjogieuah1shja'}; UEDC_LOGIN_POLICY_UPDATE_TIME=1621468800000; UEDC_LOGIN_POLICY_VALUE=checked",
        "Host": "**************",
        "Origin": "https://**************",
        "Referer": "https://**************/ui/login/login.html",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "X-Requested-With": "XMLHttpRequest",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"'
    }
    
    # 构造POST数据
    post_data = {
        "name": username,
        "password": password,
        "captcha": captcha_text,
        "loginType": "account"
    }
    
    try:
        # 创建SSL上下文
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        ssl_context.set_ciphers('DEFAULT:@SECLEVEL=1')
        
        # 编码POST数据
        post_data_encoded = urllib.parse.urlencode(post_data).encode('utf-8')
        
        # 创建请求
        req = urllib.request.Request(url, data=post_data_encoded, headers=headers, method='POST')
        
        print(f"正在登录... (用户名: {username}, 验证码: {captcha_text})")
        
        with urllib.request.urlopen(req, context=ssl_context, timeout=30) as response:
            response_bytes = response.read()
            status_code = response.getcode()
            
            print(f"登录请求状态码: {status_code}")
            
            # 处理响应
            if response_bytes.startswith(b'\x1f\x8b'):
                response_body = gzip.decompress(response_bytes).decode('utf-8')
            else:
                response_body = response_bytes.decode('utf-8')
            
            print(f"登录响应: {response_body}")
            
            # 尝试解析JSON响应
            try:
                response_data = json.loads(response_body)
                
                if response_data.get('success') or response_data.get('status') == 'success':
                    print("✓ 登录成功")
                    
                    # 提取新的session信息
                    new_session_id = None
                    set_cookie = response.getheader('Set-Cookie', '')
                    if 'sess_id=' in set_cookie:
                        import re
                        match = re.search(r'sess_id=([^;]+)', set_cookie)
                        if match:
                            new_session_id = match.group(1)
                    
                    return {
                        "success": True,
                        "session_id": new_session_id or session_id,
                        "response": response_data,
                        "captcha_used": captcha_text
                    }
                else:
                    print("❌ 登录失败")
                    return {
                        "success": False,
                        "error": response_data.get('message', '登录失败'),
                        "captcha_used": captcha_text
                    }
                    
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                return {
                    "success": False,
                    "error": "响应格式错误",
                    "raw_response": response_body
                }
                
    except Exception as e:
        print(f"登录请求失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

def get_valid_session_cookie(manual_captcha=True):
    """
    获取有效的session cookie
    """
    print("=" * 50)
    print("获取攻击告警系统有效Cookie")
    print("=" * 50)

    # 尝试登录获取新的session
    login_result = login_attack_alarm_system(manual_captcha=manual_captcha)
    
    if login_result and login_result.get('success'):
        session_id = login_result.get('session_id')
        if session_id:
            cookie_string = f"UEDC_LOGIN_PE_TIME=100000; UEDC_LOGIN_POLICY_VALUE=checked; sess_id={session_id}"
            print(f"✓ 获取到新的Cookie: {cookie_string}")
            
            # 保存登录信息
            with open('attack_alarm_session.json', 'w', encoding='utf-8') as f:
                json.dump(login_result, f, ensure_ascii=False, indent=2)
            print("✓ 登录信息已保存到 attack_alarm_session.json")
            
            return cookie_string
        else:
            print("❌ 未能获取到session_id")
            return None
    else:
        print("❌ 登录失败")
        return None

def test_captcha_recognition():
    """
    测试验证码识别功能
    """
    print("=" * 50)
    print("验证码识别测试")
    print("=" * 50)
    
    # 获取验证码
    captcha_base64, captcha_bytes = get_captcha_image()
    
    if captcha_bytes:
        # 保存验证码图片
        with open('captcha_test.png', 'wb') as f:
            f.write(captcha_bytes)
        print("✓ 验证码图片已保存到 captcha_test.png")
        
        # 识别验证码
        if DDDDOCR_AVAILABLE:
            captcha_text = recognize_captcha(captcha_bytes)
            print(f"识别结果: {captcha_text}")
        else:
            print("请安装ddddocr库: pip install ddddocr")
    else:
        print("❌ 无法获取验证码图片")

if __name__ == "__main__":
    # 测试功能
    test_captcha_recognition()
    
    # 尝试登录
    result = get_valid_session_cookie()
    if result:
        print(f"\n🎉 成功获取Cookie: {result}")
    else:
        print("\n❌ 无法获取有效Cookie")
