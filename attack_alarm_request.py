import urllib.request
import urllib.parse
import json
import ssl

def get_attack_alarm_data(auth_token=None, cookies=None):
    """
    向攻击告警API发起POST请求并提取attack_ip属性

    Args:
        auth_token (str, optional): 认证token
        cookies (str, optional): 会话cookies
    """
    # API端点
    url = "https://**************/apps/secevent/attack_alarm/attack_alarm/get_attack_alarm_lists"

    # 请求头
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "Cookie": "UEDC_LOGIN_PE_TIME=100000; UEDC_LOGIN_POLICY_VALUE=checked; sess_id=c72tunb0g",
        "Host": "**************",
        "Origin": "https://**************",
        "Referer": "https://**************/ui/",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "X-Requested-With": "XMLHttpRequest",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"'
    }

    # 添加认证信息（如果提供）
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"
    if cookies:
        headers["Cookie"] = cookies
    
    # 请求负载
    payload = {
        "start": 0,
        "limit": 100,
        "host_type": ["all"],
        "new_alert_type": ["all"],
        "attack_result": ["all"],
        "reliability": [3, 2],
        "priority": [3, 2, 1],
        "stage": ["all"],
        "tag": ["all"],
        "status": ["all"],
        "http_status_code": ["all"],
        "attack_direction": ["all"],
        "src_branch_id": ["all"],
        "dst_branch_id": ["all"],
        "dev_id": ["all"],
        "is_read": "all",
        "branch_id": ["all"],
        "affect_type": ["all"],
        "time_range": "last24h",
        "custom_search_params": [],
        "page": 1,
        "only_focus_asset": 0,
        "view_branch_id": 0
    }
    
    try:
        # 创建SSL上下文，跳过证书验证
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # 准备POST数据
        post_data = json.dumps(payload).encode('utf-8')

        # 创建请求对象
        req = urllib.request.Request(url, data=post_data, headers=headers, method='POST')

        # 发起POST请求
        print("正在发起POST请求...")
        print(f"目标URL: {url}")
        print(f"请求头: {headers}")
        print(f"负载大小: {len(post_data)} 字节")

        with urllib.request.urlopen(req, context=ssl_context, timeout=30) as response:
            # 读取响应
            response_body = response.read().decode('utf-8')
            status_code = response.getcode()

            print(f"请求成功，状态码: {status_code}")

            # 解析JSON响应
            response_data = json.loads(response_body)
            print(f"响应总数: {response_data.get('total', 0)}")
            print(f"请求成功状态: {response_data.get('success', False)}")

            # 提取data字段
            data = response_data.get('data', [])

            # 如果data是字符串，尝试解析为JSON
            if isinstance(data, str):
                try:
                    data = json.loads(data)
                except json.JSONDecodeError:
                    print("警告: data字段不是有效的JSON格式")
                    return []

            # 提取attack_ip属性
            attack_ips = []
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict) and 'attack_ip' in item:
                        attack_ips.append(item['attack_ip'])

            print(f"成功提取到 {len(attack_ips)} 个attack_ip")
            return attack_ips

    except urllib.error.HTTPError as e:
        print(f"HTTP错误: {e.code} - {e.reason}")
        return []
    except urllib.error.URLError as e:
        print(f"URL错误: {e.reason}")
        return []
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return []
    except Exception as e:
        print(f"未知错误: {e}")
        return []

def test_connection():
    """
    测试与目标服务器的连接
    """
    import socket

    host = "**************"
    port = 443  # HTTPS端口

    print(f"测试连接到 {host}:{port}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()

        if result == 0:
            print("✓ 网络连接正常")
            return True
        else:
            print("✗ 无法连接到目标服务器")
            return False
    except Exception as e:
        print(f"✗ 连接测试失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("开始获取攻击告警数据...")

    # 首先测试网络连接
    if not test_connection():
        print("\n请检查:")
        print("1. 网络连接是否正常")
        print("2. 是否在正确的网络环境中（VPN等）")
        print("3. 目标服务器是否可访问")
        return

    # 如果需要认证，可以在这里添加token或cookies
    # auth_token = "your_token_here"  # 如果需要的话
    # cookies = "session_id=xxx; csrf_token=yyy"  # 如果需要的话

    attack_ips = get_attack_alarm_data()
    
    if attack_ips:
        print("\n提取到的attack_ip列表:")
        for i, ip in enumerate(attack_ips, 1):
            print(f"{i}. {ip}")
        
        # 保存到文件
        with open('attack_ips.txt', 'w', encoding='utf-8') as f:
            for ip in attack_ips:
                f.write(f"{ip}\n")
        print(f"\nattack_ip列表已保存到 attack_ips.txt 文件")
        
        # 统计唯一IP
        unique_ips = list(set(attack_ips))
        print(f"\n统计信息:")
        print(f"总计attack_ip数量: {len(attack_ips)}")
        print(f"唯一attack_ip数量: {len(unique_ips)}")
        
    else:
        print("未能提取到任何attack_ip数据")

if __name__ == "__main__":
    main()
