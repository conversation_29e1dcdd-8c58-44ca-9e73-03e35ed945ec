Write-Host "正在发起POST请求..."
$response = curl -X POST -k --insecure --connect-timeout 30 --max-time 60 -H "Accept: application/json, text/plain, */*" -H "Accept-Encoding: gzip, deflate, br, zstd" -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" -H "Connection: keep-alive" -H "Content-Type: application/json;charset=UTF-8" -H "Cookie: UEDC_LOGIN_PE_TIME=100000; UEDC_LOGIN_POLICY_VALUE=checked; sess_id=c72tunb0g" -H "Host: **************" -H "Origin: https://**************" -H "Referer: https://**************/ui/" -H "Sec-Fetch-Dest: empty" -H "Sec-Fetch-Mode: cors" -H "Sec-Fetch-Site: same-origin" -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" -H "X-Requested-With: XMLHttpRequest" -H "sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"" -H "sec-ch-ua-mobile: ?0" -H "sec-ch-ua-platform: "Windows"" --data-binary @payload.json https://**************/apps/secevent/attack_alarm/attack_alarm/get_attack_alarm_lists
Write-Host $response
Read-Host "按Enter键退出"
